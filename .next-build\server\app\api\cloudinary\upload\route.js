/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/cloudinary/upload/route";
exports.ids = ["app/api/cloudinary/upload/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcloudinary%2Fupload%2Froute&page=%2Fapi%2Fcloudinary%2Fupload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcloudinary%2Fupload%2Froute.ts&appDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcloudinary%2Fupload%2Froute&page=%2Fapi%2Fcloudinary%2Fupload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcloudinary%2Fupload%2Froute.ts&appDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_DABBIE_OneDrive_Desktop_lgu_project_app_src_app_api_cloudinary_upload_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/cloudinary/upload/route.ts */ \"(rsc)/./src/app/api/cloudinary/upload/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/cloudinary/upload/route\",\n        pathname: \"/api/cloudinary/upload\",\n        filename: \"route\",\n        bundlePath: \"app/api/cloudinary/upload/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\lgu-project-app\\\\src\\\\app\\\\api\\\\cloudinary\\\\upload\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_DABBIE_OneDrive_Desktop_lgu_project_app_src_app_api_cloudinary_upload_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcloudinary%2Fupload%2Froute&page=%2Fapi%2Fcloudinary%2Fupload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcloudinary%2Fupload%2Froute.ts&appDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/cloudinary/upload/route.ts":
/*!************************************************!*\
  !*** ./src/app/api/cloudinary/upload/route.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_cloudinary__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/cloudinary */ \"(rsc)/./src/lib/cloudinary.ts\");\n/* harmony import */ var _lib_supabaseMediaService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabaseMediaService */ \"(rsc)/./src/lib/supabaseMediaService.ts\");\n/**\n * Cloudinary Upload API Route\n * \n * Handles server-side uploads to Cloudinary for the LGU Project.\n * Provides secure upload functionality with proper validation and error handling.\n * \n * Features:\n * - File validation (type, size)\n * - Secure uploads with API keys\n * - Database integration for metadata storage\n * - Error handling and logging\n * - Support for multiple file types\n * \n * Usage:\n * POST /api/cloudinary/upload\n * Content-Type: multipart/form-data\n * Body: { file: File, folder?: string, tags?: string[] }\n */ \n\n\n/**\n * Maximum file size (10MB)\n */ const MAX_FILE_SIZE = 10 * 1024 * 1024;\n/**\n * Allowed file types\n */ const ALLOWED_FILE_TYPES = [\n    'image/jpeg',\n    'image/jpg',\n    'image/png',\n    'image/gif',\n    'image/webp',\n    'video/mp4',\n    'video/webm',\n    'video/ogg',\n    'application/pdf',\n    'application/msword',\n    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'\n];\n/**\n * POST /api/cloudinary/upload\n * Upload files to Cloudinary\n */ async function POST(request) {\n    try {\n        console.log('[Cloudinary API] Upload request received');\n        // Parse form data\n        const formData = await request.formData();\n        const file = formData.get('file');\n        const folder = formData.get('folder') || _lib_cloudinary__WEBPACK_IMPORTED_MODULE_1__.CLOUDINARY_FOLDERS.MEDIA;\n        const tags = formData.get('tags') ? JSON.parse(formData.get('tags')) : [];\n        const public_id = formData.get('public_id');\n        const personnel_id = formData.get('personnel_id') ? parseInt(formData.get('personnel_id')) : undefined;\n        const document_type = formData.get('document_type');\n        const description = formData.get('description');\n        // Validate file\n        if (!file) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No file provided'\n            }, {\n                status: 400\n            });\n        }\n        // Validate file type\n        if (!ALLOWED_FILE_TYPES.includes(file.type)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `File type ${file.type} not allowed. Allowed types: ${ALLOWED_FILE_TYPES.join(', ')}`\n            }, {\n                status: 400\n            });\n        }\n        // Validate file size\n        if (file.size > MAX_FILE_SIZE) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `File size ${file.size} exceeds maximum allowed size of ${MAX_FILE_SIZE} bytes`\n            }, {\n                status: 400\n            });\n        }\n        console.log('[Cloudinary API] File validation passed:', {\n            name: file.name,\n            type: file.type,\n            size: file.size,\n            folder\n        });\n        // Upload to Cloudinary\n        const uploadResult = await (0,_lib_cloudinary__WEBPACK_IMPORTED_MODULE_1__.uploadToCloudinary)(file, {\n            folder,\n            tags: [\n                'lgu-project',\n                ...tags\n            ],\n            public_id,\n            resource_type: 'auto'\n        });\n        console.log('[Cloudinary API] Upload successful:', uploadResult.public_id);\n        // Store in media assets database for bidirectional sync\n        let databaseSyncSuccess = false;\n        let databaseSyncError = null;\n        try {\n            const mediaAsset = {\n                cloudinary_public_id: uploadResult.public_id,\n                cloudinary_version: uploadResult.version,\n                cloudinary_signature: uploadResult.signature,\n                cloudinary_etag: uploadResult.etag,\n                original_filename: uploadResult.original_filename || file.name,\n                file_size: uploadResult.bytes,\n                mime_type: file.type,\n                format: uploadResult.format,\n                width: uploadResult.width,\n                height: uploadResult.height,\n                folder: uploadResult.folder,\n                tags: uploadResult.tags || [],\n                description: description,\n                secure_url: uploadResult.secure_url,\n                url: uploadResult.url,\n                resource_type: uploadResult.resource_type,\n                cloudinary_created_at: uploadResult.created_at,\n                sync_status: 'synced',\n                used_in_personnel: personnel_id\n            };\n            await _lib_supabaseMediaService__WEBPACK_IMPORTED_MODULE_2__.SupabaseMediaService.upsertMediaAsset(mediaAsset);\n            // Log the upload operation\n            await _lib_supabaseMediaService__WEBPACK_IMPORTED_MODULE_2__.SupabaseMediaService.logSyncOperation({\n                operation: 'upload',\n                status: 'synced',\n                cloudinary_public_id: uploadResult.public_id,\n                source: 'admin',\n                file_size: uploadResult.bytes,\n                operation_data: {\n                    upload_result: uploadResult,\n                    personnel_id,\n                    document_type\n                }\n            });\n            databaseSyncSuccess = true;\n            console.log('[Cloudinary API] Media asset saved to database for bidirectional sync');\n        } catch (dbError) {\n            console.error('[Cloudinary API] Database sync failed:', dbError);\n            databaseSyncError = dbError instanceof Error ? dbError.message : 'Database sync failed';\n            // Don't fail the upload if database save fails, but try to log it\n            try {\n                await _lib_supabaseMediaService__WEBPACK_IMPORTED_MODULE_2__.SupabaseMediaService.logSyncOperation({\n                    operation: 'upload',\n                    status: 'error',\n                    cloudinary_public_id: uploadResult.public_id,\n                    source: 'admin',\n                    error_message: databaseSyncError,\n                    file_size: uploadResult.bytes\n                });\n            } catch (logError) {\n                console.error('[Cloudinary API] Failed to log sync error:', logError);\n            }\n        }\n        // Return success response with database sync status\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                public_id: uploadResult.public_id,\n                version: uploadResult.version,\n                signature: uploadResult.signature,\n                width: uploadResult.width,\n                height: uploadResult.height,\n                format: uploadResult.format,\n                resource_type: uploadResult.resource_type,\n                created_at: uploadResult.created_at,\n                tags: uploadResult.tags,\n                bytes: uploadResult.bytes,\n                url: uploadResult.url,\n                secure_url: uploadResult.secure_url,\n                folder: uploadResult.folder,\n                original_filename: uploadResult.original_filename\n            },\n            database_sync: {\n                success: databaseSyncSuccess,\n                error: databaseSyncError,\n                message: databaseSyncSuccess ? 'File uploaded and synced to database successfully' : 'File uploaded to Cloudinary but database sync failed - run database setup script'\n            },\n            warnings: databaseSyncSuccess ? [] : [\n                'Database sync failed - media will not persist on refresh',\n                'Run the SQL script from docs/full-complete-supabase-script.md to enable persistence',\n                'Then use the sync button to import existing files'\n            ]\n        });\n    } catch (error) {\n        console.error('[Cloudinary API] Upload failed:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Upload failed',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n/**\n * GET /api/cloudinary/upload\n * Get upload configuration and limits\n */ async function GET() {\n    try {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            config: {\n                maxFileSize: MAX_FILE_SIZE,\n                allowedFileTypes: ALLOWED_FILE_TYPES,\n                folders: _lib_cloudinary__WEBPACK_IMPORTED_MODULE_1__.CLOUDINARY_FOLDERS,\n                cloudName: \"dvwaviwn0\"\n            }\n        });\n    } catch (error) {\n        console.error('[Cloudinary API] Config fetch failed:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch configuration'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9jbG91ZGluYXJ5L3VwbG9hZC9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBOzs7Ozs7Ozs7Ozs7Ozs7OztDQWlCQyxHQUVzRDtBQUNrQjtBQUNSO0FBRWpFOztDQUVDLEdBQ0QsTUFBTUksZ0JBQWdCLEtBQUssT0FBTztBQUVsQzs7Q0FFQyxHQUNELE1BQU1DLHFCQUFxQjtJQUN6QjtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0NBQ0Q7QUFJRDs7O0NBR0MsR0FDTSxlQUFlQyxLQUFLQyxPQUFvQjtJQUM3QyxJQUFJO1FBQ0ZDLFFBQVFDLEdBQUcsQ0FBQztRQUVaLGtCQUFrQjtRQUNsQixNQUFNQyxXQUFXLE1BQU1ILFFBQVFHLFFBQVE7UUFDdkMsTUFBTUMsT0FBT0QsU0FBU0UsR0FBRyxDQUFDO1FBQzFCLE1BQU1DLFNBQVNILFNBQVNFLEdBQUcsQ0FBQyxhQUF1QlYsK0RBQWtCQSxDQUFDWSxLQUFLO1FBQzNFLE1BQU1DLE9BQU9MLFNBQVNFLEdBQUcsQ0FBQyxVQUFVSSxLQUFLQyxLQUFLLENBQUNQLFNBQVNFLEdBQUcsQ0FBQyxXQUFxQixFQUFFO1FBQ25GLE1BQU1NLFlBQVlSLFNBQVNFLEdBQUcsQ0FBQztRQUMvQixNQUFNTyxlQUFlVCxTQUFTRSxHQUFHLENBQUMsa0JBQWtCUSxTQUFTVixTQUFTRSxHQUFHLENBQUMsbUJBQTZCUztRQUN2RyxNQUFNQyxnQkFBZ0JaLFNBQVNFLEdBQUcsQ0FBQztRQUNuQyxNQUFNVyxjQUFjYixTQUFTRSxHQUFHLENBQUM7UUFFakMsZ0JBQWdCO1FBQ2hCLElBQUksQ0FBQ0QsTUFBTTtZQUNULE9BQU9YLHFEQUFZQSxDQUFDd0IsSUFBSSxDQUN0QjtnQkFBRUMsT0FBTztZQUFtQixHQUM1QjtnQkFBRUMsUUFBUTtZQUFJO1FBRWxCO1FBRUEscUJBQXFCO1FBQ3JCLElBQUksQ0FBQ3JCLG1CQUFtQnNCLFFBQVEsQ0FBQ2hCLEtBQUtpQixJQUFJLEdBQUc7WUFDM0MsT0FBTzVCLHFEQUFZQSxDQUFDd0IsSUFBSSxDQUN0QjtnQkFBRUMsT0FBTyxDQUFDLFVBQVUsRUFBRWQsS0FBS2lCLElBQUksQ0FBQyw2QkFBNkIsRUFBRXZCLG1CQUFtQndCLElBQUksQ0FBQyxPQUFPO1lBQUMsR0FDL0Y7Z0JBQUVILFFBQVE7WUFBSTtRQUVsQjtRQUVBLHFCQUFxQjtRQUNyQixJQUFJZixLQUFLbUIsSUFBSSxHQUFHMUIsZUFBZTtZQUM3QixPQUFPSixxREFBWUEsQ0FBQ3dCLElBQUksQ0FDdEI7Z0JBQUVDLE9BQU8sQ0FBQyxVQUFVLEVBQUVkLEtBQUttQixJQUFJLENBQUMsaUNBQWlDLEVBQUUxQixjQUFjLE1BQU0sQ0FBQztZQUFDLEdBQ3pGO2dCQUFFc0IsUUFBUTtZQUFJO1FBRWxCO1FBRUFsQixRQUFRQyxHQUFHLENBQUMsNENBQTRDO1lBQ3REc0IsTUFBTXBCLEtBQUtvQixJQUFJO1lBQ2ZILE1BQU1qQixLQUFLaUIsSUFBSTtZQUNmRSxNQUFNbkIsS0FBS21CLElBQUk7WUFDZmpCO1FBQ0Y7UUFFQSx1QkFBdUI7UUFDdkIsTUFBTW1CLGVBQWUsTUFBTS9CLG1FQUFrQkEsQ0FBQ1UsTUFBTTtZQUNsREU7WUFDQUUsTUFBTTtnQkFBQzttQkFBa0JBO2FBQUs7WUFDOUJHO1lBQ0FlLGVBQWU7UUFDakI7UUFFQXpCLFFBQVFDLEdBQUcsQ0FBQyx1Q0FBdUN1QixhQUFhZCxTQUFTO1FBRXpFLHdEQUF3RDtRQUN4RCxJQUFJZ0Isc0JBQXNCO1FBQzFCLElBQUlDLG9CQUFvQjtRQUV4QixJQUFJO1lBQ0YsTUFBTUMsYUFBYTtnQkFDakJDLHNCQUFzQkwsYUFBYWQsU0FBUztnQkFDNUNvQixvQkFBb0JOLGFBQWFPLE9BQU87Z0JBQ3hDQyxzQkFBc0JSLGFBQWFTLFNBQVM7Z0JBQzVDQyxpQkFBaUJWLGFBQWFXLElBQUk7Z0JBQ2xDQyxtQkFBbUJaLGFBQWFZLGlCQUFpQixJQUFJakMsS0FBS29CLElBQUk7Z0JBQzlEYyxXQUFXYixhQUFhYyxLQUFLO2dCQUM3QkMsV0FBV3BDLEtBQUtpQixJQUFJO2dCQUNwQm9CLFFBQVFoQixhQUFhZ0IsTUFBTTtnQkFDM0JDLE9BQU9qQixhQUFhaUIsS0FBSztnQkFDekJDLFFBQVFsQixhQUFha0IsTUFBTTtnQkFDM0JyQyxRQUFRbUIsYUFBYW5CLE1BQU07Z0JBQzNCRSxNQUFNaUIsYUFBYWpCLElBQUksSUFBSSxFQUFFO2dCQUM3QlEsYUFBYUE7Z0JBQ2I0QixZQUFZbkIsYUFBYW1CLFVBQVU7Z0JBQ25DQyxLQUFLcEIsYUFBYW9CLEdBQUc7Z0JBQ3JCbkIsZUFBZUQsYUFBYUMsYUFBYTtnQkFDekNvQix1QkFBdUJyQixhQUFhc0IsVUFBVTtnQkFDOUNDLGFBQWE7Z0JBQ2JDLG1CQUFtQnJDO1lBQ3JCO1lBRUEsTUFBTWhCLDJFQUFvQkEsQ0FBQ3NELGdCQUFnQixDQUFDckI7WUFFNUMsMkJBQTJCO1lBQzNCLE1BQU1qQywyRUFBb0JBLENBQUN1RCxnQkFBZ0IsQ0FBQztnQkFDMUNDLFdBQVc7Z0JBQ1hqQyxRQUFRO2dCQUNSVyxzQkFBc0JMLGFBQWFkLFNBQVM7Z0JBQzVDMEMsUUFBUTtnQkFDUmYsV0FBV2IsYUFBYWMsS0FBSztnQkFDN0JlLGdCQUFnQjtvQkFDZEMsZUFBZTlCO29CQUNmYjtvQkFDQUc7Z0JBQ0Y7WUFDRjtZQUVBWSxzQkFBc0I7WUFDdEIxQixRQUFRQyxHQUFHLENBQUM7UUFFZCxFQUFFLE9BQU9zRCxTQUFTO1lBQ2hCdkQsUUFBUWlCLEtBQUssQ0FBQywwQ0FBMENzQztZQUN4RDVCLG9CQUFvQjRCLG1CQUFtQkMsUUFBUUQsUUFBUUUsT0FBTyxHQUFHO1lBRWpFLGtFQUFrRTtZQUNsRSxJQUFJO2dCQUNGLE1BQU05RCwyRUFBb0JBLENBQUN1RCxnQkFBZ0IsQ0FBQztvQkFDMUNDLFdBQVc7b0JBQ1hqQyxRQUFRO29CQUNSVyxzQkFBc0JMLGFBQWFkLFNBQVM7b0JBQzVDMEMsUUFBUTtvQkFDUk0sZUFBZS9CO29CQUNmVSxXQUFXYixhQUFhYyxLQUFLO2dCQUMvQjtZQUNGLEVBQUUsT0FBT3FCLFVBQVU7Z0JBQ2pCM0QsUUFBUWlCLEtBQUssQ0FBQyw4Q0FBOEMwQztZQUM5RDtRQUNGO1FBRUEsb0RBQW9EO1FBQ3BELE9BQU9uRSxxREFBWUEsQ0FBQ3dCLElBQUksQ0FBQztZQUN2QjRDLFNBQVM7WUFDVEMsTUFBTTtnQkFDSm5ELFdBQVdjLGFBQWFkLFNBQVM7Z0JBQ2pDcUIsU0FBU1AsYUFBYU8sT0FBTztnQkFDN0JFLFdBQVdULGFBQWFTLFNBQVM7Z0JBQ2pDUSxPQUFPakIsYUFBYWlCLEtBQUs7Z0JBQ3pCQyxRQUFRbEIsYUFBYWtCLE1BQU07Z0JBQzNCRixRQUFRaEIsYUFBYWdCLE1BQU07Z0JBQzNCZixlQUFlRCxhQUFhQyxhQUFhO2dCQUN6Q3FCLFlBQVl0QixhQUFhc0IsVUFBVTtnQkFDbkN2QyxNQUFNaUIsYUFBYWpCLElBQUk7Z0JBQ3ZCK0IsT0FBT2QsYUFBYWMsS0FBSztnQkFDekJNLEtBQUtwQixhQUFhb0IsR0FBRztnQkFDckJELFlBQVluQixhQUFhbUIsVUFBVTtnQkFDbkN0QyxRQUFRbUIsYUFBYW5CLE1BQU07Z0JBQzNCK0IsbUJBQW1CWixhQUFhWSxpQkFBaUI7WUFDbkQ7WUFDQTBCLGVBQWU7Z0JBQ2JGLFNBQVNsQztnQkFDVFQsT0FBT1U7Z0JBQ1A4QixTQUFTL0Isc0JBQ0wsc0RBQ0E7WUFDTjtZQUNBcUMsVUFBVXJDLHNCQUFzQixFQUFFLEdBQUc7Z0JBQ25DO2dCQUNBO2dCQUNBO2FBQ0Q7UUFDSDtJQUVGLEVBQUUsT0FBT1QsT0FBTztRQUNkakIsUUFBUWlCLEtBQUssQ0FBQyxtQ0FBbUNBO1FBRWpELE9BQU96QixxREFBWUEsQ0FBQ3dCLElBQUksQ0FDdEI7WUFDRUMsT0FBTztZQUNQK0MsU0FBUy9DLGlCQUFpQnVDLFFBQVF2QyxNQUFNd0MsT0FBTyxHQUFHO1FBQ3BELEdBQ0E7WUFBRXZDLFFBQVE7UUFBSTtJQUVsQjtBQUNGO0FBRUE7OztDQUdDLEdBQ00sZUFBZStDO0lBQ3BCLElBQUk7UUFDRixPQUFPekUscURBQVlBLENBQUN3QixJQUFJLENBQUM7WUFDdkI0QyxTQUFTO1lBQ1RNLFFBQVE7Z0JBQ05DLGFBQWF2RTtnQkFDYndFLGtCQUFrQnZFO2dCQUNsQndFLFNBQVMzRSwrREFBa0JBO2dCQUMzQjRFLFdBQVdDLFdBQTZDO1lBQzFEO1FBQ0Y7SUFDRixFQUFFLE9BQU90RCxPQUFPO1FBQ2RqQixRQUFRaUIsS0FBSyxDQUFDLHlDQUF5Q0E7UUFFdkQsT0FBT3pCLHFEQUFZQSxDQUFDd0IsSUFBSSxDQUN0QjtZQUFFQyxPQUFPO1FBQWdDLEdBQ3pDO1lBQUVDLFFBQVE7UUFBSTtJQUVsQjtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERBQkJJRVxcT25lRHJpdmVcXERlc2t0b3BcXGxndS1wcm9qZWN0LWFwcFxcc3JjXFxhcHBcXGFwaVxcY2xvdWRpbmFyeVxcdXBsb2FkXFxyb3V0ZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIENsb3VkaW5hcnkgVXBsb2FkIEFQSSBSb3V0ZVxuICogXG4gKiBIYW5kbGVzIHNlcnZlci1zaWRlIHVwbG9hZHMgdG8gQ2xvdWRpbmFyeSBmb3IgdGhlIExHVSBQcm9qZWN0LlxuICogUHJvdmlkZXMgc2VjdXJlIHVwbG9hZCBmdW5jdGlvbmFsaXR5IHdpdGggcHJvcGVyIHZhbGlkYXRpb24gYW5kIGVycm9yIGhhbmRsaW5nLlxuICogXG4gKiBGZWF0dXJlczpcbiAqIC0gRmlsZSB2YWxpZGF0aW9uICh0eXBlLCBzaXplKVxuICogLSBTZWN1cmUgdXBsb2FkcyB3aXRoIEFQSSBrZXlzXG4gKiAtIERhdGFiYXNlIGludGVncmF0aW9uIGZvciBtZXRhZGF0YSBzdG9yYWdlXG4gKiAtIEVycm9yIGhhbmRsaW5nIGFuZCBsb2dnaW5nXG4gKiAtIFN1cHBvcnQgZm9yIG11bHRpcGxlIGZpbGUgdHlwZXNcbiAqIFxuICogVXNhZ2U6XG4gKiBQT1NUIC9hcGkvY2xvdWRpbmFyeS91cGxvYWRcbiAqIENvbnRlbnQtVHlwZTogbXVsdGlwYXJ0L2Zvcm0tZGF0YVxuICogQm9keTogeyBmaWxlOiBGaWxlLCBmb2xkZXI/OiBzdHJpbmcsIHRhZ3M/OiBzdHJpbmdbXSB9XG4gKi9cblxuaW1wb3J0IHsgTmV4dFJlcXVlc3QsIE5leHRSZXNwb25zZSB9IGZyb20gJ25leHQvc2VydmVyJ1xuaW1wb3J0IHsgdXBsb2FkVG9DbG91ZGluYXJ5LCBDTE9VRElOQVJZX0ZPTERFUlMgfSBmcm9tICdAL2xpYi9jbG91ZGluYXJ5J1xuaW1wb3J0IHsgU3VwYWJhc2VNZWRpYVNlcnZpY2UgfSBmcm9tICdAL2xpYi9zdXBhYmFzZU1lZGlhU2VydmljZSdcblxuLyoqXG4gKiBNYXhpbXVtIGZpbGUgc2l6ZSAoMTBNQilcbiAqL1xuY29uc3QgTUFYX0ZJTEVfU0laRSA9IDEwICogMTAyNCAqIDEwMjRcblxuLyoqXG4gKiBBbGxvd2VkIGZpbGUgdHlwZXNcbiAqL1xuY29uc3QgQUxMT1dFRF9GSUxFX1RZUEVTID0gW1xuICAnaW1hZ2UvanBlZycsXG4gICdpbWFnZS9qcGcnLFxuICAnaW1hZ2UvcG5nJyxcbiAgJ2ltYWdlL2dpZicsXG4gICdpbWFnZS93ZWJwJyxcbiAgJ3ZpZGVvL21wNCcsXG4gICd2aWRlby93ZWJtJyxcbiAgJ3ZpZGVvL29nZycsXG4gICdhcHBsaWNhdGlvbi9wZGYnLFxuICAnYXBwbGljYXRpb24vbXN3b3JkJyxcbiAgJ2FwcGxpY2F0aW9uL3ZuZC5vcGVueG1sZm9ybWF0cy1vZmZpY2Vkb2N1bWVudC53b3JkcHJvY2Vzc2luZ21sLmRvY3VtZW50J1xuXVxuXG5cblxuLyoqXG4gKiBQT1NUIC9hcGkvY2xvdWRpbmFyeS91cGxvYWRcbiAqIFVwbG9hZCBmaWxlcyB0byBDbG91ZGluYXJ5XG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBQT1NUKHJlcXVlc3Q6IE5leHRSZXF1ZXN0KSB7XG4gIHRyeSB7XG4gICAgY29uc29sZS5sb2coJ1tDbG91ZGluYXJ5IEFQSV0gVXBsb2FkIHJlcXVlc3QgcmVjZWl2ZWQnKVxuXG4gICAgLy8gUGFyc2UgZm9ybSBkYXRhXG4gICAgY29uc3QgZm9ybURhdGEgPSBhd2FpdCByZXF1ZXN0LmZvcm1EYXRhKClcbiAgICBjb25zdCBmaWxlID0gZm9ybURhdGEuZ2V0KCdmaWxlJykgYXMgRmlsZVxuICAgIGNvbnN0IGZvbGRlciA9IGZvcm1EYXRhLmdldCgnZm9sZGVyJykgYXMgc3RyaW5nIHx8IENMT1VESU5BUllfRk9MREVSUy5NRURJQVxuICAgIGNvbnN0IHRhZ3MgPSBmb3JtRGF0YS5nZXQoJ3RhZ3MnKSA/IEpTT04ucGFyc2UoZm9ybURhdGEuZ2V0KCd0YWdzJykgYXMgc3RyaW5nKSA6IFtdXG4gICAgY29uc3QgcHVibGljX2lkID0gZm9ybURhdGEuZ2V0KCdwdWJsaWNfaWQnKSBhcyBzdHJpbmdcbiAgICBjb25zdCBwZXJzb25uZWxfaWQgPSBmb3JtRGF0YS5nZXQoJ3BlcnNvbm5lbF9pZCcpID8gcGFyc2VJbnQoZm9ybURhdGEuZ2V0KCdwZXJzb25uZWxfaWQnKSBhcyBzdHJpbmcpIDogdW5kZWZpbmVkXG4gICAgY29uc3QgZG9jdW1lbnRfdHlwZSA9IGZvcm1EYXRhLmdldCgnZG9jdW1lbnRfdHlwZScpIGFzIHN0cmluZ1xuICAgIGNvbnN0IGRlc2NyaXB0aW9uID0gZm9ybURhdGEuZ2V0KCdkZXNjcmlwdGlvbicpIGFzIHN0cmluZ1xuXG4gICAgLy8gVmFsaWRhdGUgZmlsZVxuICAgIGlmICghZmlsZSkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IGVycm9yOiAnTm8gZmlsZSBwcm92aWRlZCcgfSxcbiAgICAgICAgeyBzdGF0dXM6IDQwMCB9XG4gICAgICApXG4gICAgfVxuXG4gICAgLy8gVmFsaWRhdGUgZmlsZSB0eXBlXG4gICAgaWYgKCFBTExPV0VEX0ZJTEVfVFlQRVMuaW5jbHVkZXMoZmlsZS50eXBlKSkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IGVycm9yOiBgRmlsZSB0eXBlICR7ZmlsZS50eXBlfSBub3QgYWxsb3dlZC4gQWxsb3dlZCB0eXBlczogJHtBTExPV0VEX0ZJTEVfVFlQRVMuam9pbignLCAnKX1gIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgKVxuICAgIH1cblxuICAgIC8vIFZhbGlkYXRlIGZpbGUgc2l6ZVxuICAgIGlmIChmaWxlLnNpemUgPiBNQVhfRklMRV9TSVpFKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgZXJyb3I6IGBGaWxlIHNpemUgJHtmaWxlLnNpemV9IGV4Y2VlZHMgbWF4aW11bSBhbGxvd2VkIHNpemUgb2YgJHtNQVhfRklMRV9TSVpFfSBieXRlc2AgfSxcbiAgICAgICAgeyBzdGF0dXM6IDQwMCB9XG4gICAgICApXG4gICAgfVxuXG4gICAgY29uc29sZS5sb2coJ1tDbG91ZGluYXJ5IEFQSV0gRmlsZSB2YWxpZGF0aW9uIHBhc3NlZDonLCB7XG4gICAgICBuYW1lOiBmaWxlLm5hbWUsXG4gICAgICB0eXBlOiBmaWxlLnR5cGUsXG4gICAgICBzaXplOiBmaWxlLnNpemUsXG4gICAgICBmb2xkZXJcbiAgICB9KVxuXG4gICAgLy8gVXBsb2FkIHRvIENsb3VkaW5hcnlcbiAgICBjb25zdCB1cGxvYWRSZXN1bHQgPSBhd2FpdCB1cGxvYWRUb0Nsb3VkaW5hcnkoZmlsZSwge1xuICAgICAgZm9sZGVyLFxuICAgICAgdGFnczogWydsZ3UtcHJvamVjdCcsIC4uLnRhZ3NdLFxuICAgICAgcHVibGljX2lkLFxuICAgICAgcmVzb3VyY2VfdHlwZTogJ2F1dG8nXG4gICAgfSlcblxuICAgIGNvbnNvbGUubG9nKCdbQ2xvdWRpbmFyeSBBUEldIFVwbG9hZCBzdWNjZXNzZnVsOicsIHVwbG9hZFJlc3VsdC5wdWJsaWNfaWQpXG5cbiAgICAvLyBTdG9yZSBpbiBtZWRpYSBhc3NldHMgZGF0YWJhc2UgZm9yIGJpZGlyZWN0aW9uYWwgc3luY1xuICAgIGxldCBkYXRhYmFzZVN5bmNTdWNjZXNzID0gZmFsc2VcbiAgICBsZXQgZGF0YWJhc2VTeW5jRXJyb3IgPSBudWxsXG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgbWVkaWFBc3NldCA9IHtcbiAgICAgICAgY2xvdWRpbmFyeV9wdWJsaWNfaWQ6IHVwbG9hZFJlc3VsdC5wdWJsaWNfaWQsXG4gICAgICAgIGNsb3VkaW5hcnlfdmVyc2lvbjogdXBsb2FkUmVzdWx0LnZlcnNpb24sXG4gICAgICAgIGNsb3VkaW5hcnlfc2lnbmF0dXJlOiB1cGxvYWRSZXN1bHQuc2lnbmF0dXJlLFxuICAgICAgICBjbG91ZGluYXJ5X2V0YWc6IHVwbG9hZFJlc3VsdC5ldGFnLFxuICAgICAgICBvcmlnaW5hbF9maWxlbmFtZTogdXBsb2FkUmVzdWx0Lm9yaWdpbmFsX2ZpbGVuYW1lIHx8IGZpbGUubmFtZSxcbiAgICAgICAgZmlsZV9zaXplOiB1cGxvYWRSZXN1bHQuYnl0ZXMsXG4gICAgICAgIG1pbWVfdHlwZTogZmlsZS50eXBlLFxuICAgICAgICBmb3JtYXQ6IHVwbG9hZFJlc3VsdC5mb3JtYXQsXG4gICAgICAgIHdpZHRoOiB1cGxvYWRSZXN1bHQud2lkdGgsXG4gICAgICAgIGhlaWdodDogdXBsb2FkUmVzdWx0LmhlaWdodCxcbiAgICAgICAgZm9sZGVyOiB1cGxvYWRSZXN1bHQuZm9sZGVyLFxuICAgICAgICB0YWdzOiB1cGxvYWRSZXN1bHQudGFncyB8fCBbXSxcbiAgICAgICAgZGVzY3JpcHRpb246IGRlc2NyaXB0aW9uLFxuICAgICAgICBzZWN1cmVfdXJsOiB1cGxvYWRSZXN1bHQuc2VjdXJlX3VybCxcbiAgICAgICAgdXJsOiB1cGxvYWRSZXN1bHQudXJsLFxuICAgICAgICByZXNvdXJjZV90eXBlOiB1cGxvYWRSZXN1bHQucmVzb3VyY2VfdHlwZSBhcyAnaW1hZ2UnIHwgJ3ZpZGVvJyB8ICdyYXcnLFxuICAgICAgICBjbG91ZGluYXJ5X2NyZWF0ZWRfYXQ6IHVwbG9hZFJlc3VsdC5jcmVhdGVkX2F0LFxuICAgICAgICBzeW5jX3N0YXR1czogJ3N5bmNlZCcgYXMgY29uc3QsXG4gICAgICAgIHVzZWRfaW5fcGVyc29ubmVsOiBwZXJzb25uZWxfaWRcbiAgICAgIH1cblxuICAgICAgYXdhaXQgU3VwYWJhc2VNZWRpYVNlcnZpY2UudXBzZXJ0TWVkaWFBc3NldChtZWRpYUFzc2V0KVxuXG4gICAgICAvLyBMb2cgdGhlIHVwbG9hZCBvcGVyYXRpb25cbiAgICAgIGF3YWl0IFN1cGFiYXNlTWVkaWFTZXJ2aWNlLmxvZ1N5bmNPcGVyYXRpb24oe1xuICAgICAgICBvcGVyYXRpb246ICd1cGxvYWQnLFxuICAgICAgICBzdGF0dXM6ICdzeW5jZWQnLFxuICAgICAgICBjbG91ZGluYXJ5X3B1YmxpY19pZDogdXBsb2FkUmVzdWx0LnB1YmxpY19pZCxcbiAgICAgICAgc291cmNlOiAnYWRtaW4nLFxuICAgICAgICBmaWxlX3NpemU6IHVwbG9hZFJlc3VsdC5ieXRlcyxcbiAgICAgICAgb3BlcmF0aW9uX2RhdGE6IHtcbiAgICAgICAgICB1cGxvYWRfcmVzdWx0OiB1cGxvYWRSZXN1bHQsXG4gICAgICAgICAgcGVyc29ubmVsX2lkLFxuICAgICAgICAgIGRvY3VtZW50X3R5cGVcbiAgICAgICAgfVxuICAgICAgfSlcblxuICAgICAgZGF0YWJhc2VTeW5jU3VjY2VzcyA9IHRydWVcbiAgICAgIGNvbnNvbGUubG9nKCdbQ2xvdWRpbmFyeSBBUEldIE1lZGlhIGFzc2V0IHNhdmVkIHRvIGRhdGFiYXNlIGZvciBiaWRpcmVjdGlvbmFsIHN5bmMnKVxuXG4gICAgfSBjYXRjaCAoZGJFcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignW0Nsb3VkaW5hcnkgQVBJXSBEYXRhYmFzZSBzeW5jIGZhaWxlZDonLCBkYkVycm9yKVxuICAgICAgZGF0YWJhc2VTeW5jRXJyb3IgPSBkYkVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBkYkVycm9yLm1lc3NhZ2UgOiAnRGF0YWJhc2Ugc3luYyBmYWlsZWQnXG5cbiAgICAgIC8vIERvbid0IGZhaWwgdGhlIHVwbG9hZCBpZiBkYXRhYmFzZSBzYXZlIGZhaWxzLCBidXQgdHJ5IHRvIGxvZyBpdFxuICAgICAgdHJ5IHtcbiAgICAgICAgYXdhaXQgU3VwYWJhc2VNZWRpYVNlcnZpY2UubG9nU3luY09wZXJhdGlvbih7XG4gICAgICAgICAgb3BlcmF0aW9uOiAndXBsb2FkJyxcbiAgICAgICAgICBzdGF0dXM6ICdlcnJvcicsXG4gICAgICAgICAgY2xvdWRpbmFyeV9wdWJsaWNfaWQ6IHVwbG9hZFJlc3VsdC5wdWJsaWNfaWQsXG4gICAgICAgICAgc291cmNlOiAnYWRtaW4nLFxuICAgICAgICAgIGVycm9yX21lc3NhZ2U6IGRhdGFiYXNlU3luY0Vycm9yLFxuICAgICAgICAgIGZpbGVfc2l6ZTogdXBsb2FkUmVzdWx0LmJ5dGVzXG4gICAgICAgIH0pXG4gICAgICB9IGNhdGNoIChsb2dFcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdbQ2xvdWRpbmFyeSBBUEldIEZhaWxlZCB0byBsb2cgc3luYyBlcnJvcjonLCBsb2dFcnJvcilcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBSZXR1cm4gc3VjY2VzcyByZXNwb25zZSB3aXRoIGRhdGFiYXNlIHN5bmMgc3RhdHVzXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICBkYXRhOiB7XG4gICAgICAgIHB1YmxpY19pZDogdXBsb2FkUmVzdWx0LnB1YmxpY19pZCxcbiAgICAgICAgdmVyc2lvbjogdXBsb2FkUmVzdWx0LnZlcnNpb24sXG4gICAgICAgIHNpZ25hdHVyZTogdXBsb2FkUmVzdWx0LnNpZ25hdHVyZSxcbiAgICAgICAgd2lkdGg6IHVwbG9hZFJlc3VsdC53aWR0aCxcbiAgICAgICAgaGVpZ2h0OiB1cGxvYWRSZXN1bHQuaGVpZ2h0LFxuICAgICAgICBmb3JtYXQ6IHVwbG9hZFJlc3VsdC5mb3JtYXQsXG4gICAgICAgIHJlc291cmNlX3R5cGU6IHVwbG9hZFJlc3VsdC5yZXNvdXJjZV90eXBlLFxuICAgICAgICBjcmVhdGVkX2F0OiB1cGxvYWRSZXN1bHQuY3JlYXRlZF9hdCxcbiAgICAgICAgdGFnczogdXBsb2FkUmVzdWx0LnRhZ3MsXG4gICAgICAgIGJ5dGVzOiB1cGxvYWRSZXN1bHQuYnl0ZXMsXG4gICAgICAgIHVybDogdXBsb2FkUmVzdWx0LnVybCxcbiAgICAgICAgc2VjdXJlX3VybDogdXBsb2FkUmVzdWx0LnNlY3VyZV91cmwsXG4gICAgICAgIGZvbGRlcjogdXBsb2FkUmVzdWx0LmZvbGRlcixcbiAgICAgICAgb3JpZ2luYWxfZmlsZW5hbWU6IHVwbG9hZFJlc3VsdC5vcmlnaW5hbF9maWxlbmFtZVxuICAgICAgfSxcbiAgICAgIGRhdGFiYXNlX3N5bmM6IHtcbiAgICAgICAgc3VjY2VzczogZGF0YWJhc2VTeW5jU3VjY2VzcyxcbiAgICAgICAgZXJyb3I6IGRhdGFiYXNlU3luY0Vycm9yLFxuICAgICAgICBtZXNzYWdlOiBkYXRhYmFzZVN5bmNTdWNjZXNzXG4gICAgICAgICAgPyAnRmlsZSB1cGxvYWRlZCBhbmQgc3luY2VkIHRvIGRhdGFiYXNlIHN1Y2Nlc3NmdWxseSdcbiAgICAgICAgICA6ICdGaWxlIHVwbG9hZGVkIHRvIENsb3VkaW5hcnkgYnV0IGRhdGFiYXNlIHN5bmMgZmFpbGVkIC0gcnVuIGRhdGFiYXNlIHNldHVwIHNjcmlwdCdcbiAgICAgIH0sXG4gICAgICB3YXJuaW5nczogZGF0YWJhc2VTeW5jU3VjY2VzcyA/IFtdIDogW1xuICAgICAgICAnRGF0YWJhc2Ugc3luYyBmYWlsZWQgLSBtZWRpYSB3aWxsIG5vdCBwZXJzaXN0IG9uIHJlZnJlc2gnLFxuICAgICAgICAnUnVuIHRoZSBTUUwgc2NyaXB0IGZyb20gZG9jcy9mdWxsLWNvbXBsZXRlLXN1cGFiYXNlLXNjcmlwdC5tZCB0byBlbmFibGUgcGVyc2lzdGVuY2UnLFxuICAgICAgICAnVGhlbiB1c2UgdGhlIHN5bmMgYnV0dG9uIHRvIGltcG9ydCBleGlzdGluZyBmaWxlcydcbiAgICAgIF1cbiAgICB9KVxuXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignW0Nsb3VkaW5hcnkgQVBJXSBVcGxvYWQgZmFpbGVkOicsIGVycm9yKVxuICAgIFxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgXG4gICAgICAgIGVycm9yOiAnVXBsb2FkIGZhaWxlZCcsXG4gICAgICAgIGRldGFpbHM6IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ1Vua25vd24gZXJyb3InXG4gICAgICB9LFxuICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgKVxuICB9XG59XG5cbi8qKlxuICogR0VUIC9hcGkvY2xvdWRpbmFyeS91cGxvYWRcbiAqIEdldCB1cGxvYWQgY29uZmlndXJhdGlvbiBhbmQgbGltaXRzXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBHRVQoKSB7XG4gIHRyeSB7XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICBjb25maWc6IHtcbiAgICAgICAgbWF4RmlsZVNpemU6IE1BWF9GSUxFX1NJWkUsXG4gICAgICAgIGFsbG93ZWRGaWxlVHlwZXM6IEFMTE9XRURfRklMRV9UWVBFUyxcbiAgICAgICAgZm9sZGVyczogQ0xPVURJTkFSWV9GT0xERVJTLFxuICAgICAgICBjbG91ZE5hbWU6IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0NMT1VESU5BUllfQ0xPVURfTkFNRVxuICAgICAgfVxuICAgIH0pXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignW0Nsb3VkaW5hcnkgQVBJXSBDb25maWcgZmV0Y2ggZmFpbGVkOicsIGVycm9yKVxuICAgIFxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgZXJyb3I6ICdGYWlsZWQgdG8gZmV0Y2ggY29uZmlndXJhdGlvbicgfSxcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgIClcbiAgfVxufVxuIl0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsInVwbG9hZFRvQ2xvdWRpbmFyeSIsIkNMT1VESU5BUllfRk9MREVSUyIsIlN1cGFiYXNlTWVkaWFTZXJ2aWNlIiwiTUFYX0ZJTEVfU0laRSIsIkFMTE9XRURfRklMRV9UWVBFUyIsIlBPU1QiLCJyZXF1ZXN0IiwiY29uc29sZSIsImxvZyIsImZvcm1EYXRhIiwiZmlsZSIsImdldCIsImZvbGRlciIsIk1FRElBIiwidGFncyIsIkpTT04iLCJwYXJzZSIsInB1YmxpY19pZCIsInBlcnNvbm5lbF9pZCIsInBhcnNlSW50IiwidW5kZWZpbmVkIiwiZG9jdW1lbnRfdHlwZSIsImRlc2NyaXB0aW9uIiwianNvbiIsImVycm9yIiwic3RhdHVzIiwiaW5jbHVkZXMiLCJ0eXBlIiwiam9pbiIsInNpemUiLCJuYW1lIiwidXBsb2FkUmVzdWx0IiwicmVzb3VyY2VfdHlwZSIsImRhdGFiYXNlU3luY1N1Y2Nlc3MiLCJkYXRhYmFzZVN5bmNFcnJvciIsIm1lZGlhQXNzZXQiLCJjbG91ZGluYXJ5X3B1YmxpY19pZCIsImNsb3VkaW5hcnlfdmVyc2lvbiIsInZlcnNpb24iLCJjbG91ZGluYXJ5X3NpZ25hdHVyZSIsInNpZ25hdHVyZSIsImNsb3VkaW5hcnlfZXRhZyIsImV0YWciLCJvcmlnaW5hbF9maWxlbmFtZSIsImZpbGVfc2l6ZSIsImJ5dGVzIiwibWltZV90eXBlIiwiZm9ybWF0Iiwid2lkdGgiLCJoZWlnaHQiLCJzZWN1cmVfdXJsIiwidXJsIiwiY2xvdWRpbmFyeV9jcmVhdGVkX2F0IiwiY3JlYXRlZF9hdCIsInN5bmNfc3RhdHVzIiwidXNlZF9pbl9wZXJzb25uZWwiLCJ1cHNlcnRNZWRpYUFzc2V0IiwibG9nU3luY09wZXJhdGlvbiIsIm9wZXJhdGlvbiIsInNvdXJjZSIsIm9wZXJhdGlvbl9kYXRhIiwidXBsb2FkX3Jlc3VsdCIsImRiRXJyb3IiLCJFcnJvciIsIm1lc3NhZ2UiLCJlcnJvcl9tZXNzYWdlIiwibG9nRXJyb3IiLCJzdWNjZXNzIiwiZGF0YSIsImRhdGFiYXNlX3N5bmMiLCJ3YXJuaW5ncyIsImRldGFpbHMiLCJHRVQiLCJjb25maWciLCJtYXhGaWxlU2l6ZSIsImFsbG93ZWRGaWxlVHlwZXMiLCJmb2xkZXJzIiwiY2xvdWROYW1lIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0NMT1VESU5BUllfQ0xPVURfTkFNRSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/cloudinary/upload/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/cloudinary.ts":
/*!*******************************!*\
  !*** ./src/lib/cloudinary.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CLOUDINARY_CONFIG: () => (/* binding */ CLOUDINARY_CONFIG),\n/* harmony export */   CLOUDINARY_FOLDERS: () => (/* binding */ CLOUDINARY_FOLDERS),\n/* harmony export */   UPLOAD_PRESETS: () => (/* binding */ UPLOAD_PRESETS),\n/* harmony export */   cloudinary: () => (/* binding */ cloudinary),\n/* harmony export */   deleteFromCloudinary: () => (/* binding */ deleteFromCloudinary),\n/* harmony export */   generateCloudinaryUrl: () => (/* binding */ generateCloudinaryUrl),\n/* harmony export */   getOptimizedImageUrl: () => (/* binding */ getOptimizedImageUrl),\n/* harmony export */   uploadToCloudinary: () => (/* binding */ uploadToCloudinary)\n/* harmony export */ });\n/**\n * Cloudinary Configuration and Utilities\n *\n * This module provides configuration and utility functions for Cloudinary integration\n * in the LGU Project. It handles image uploads, transformations, and media management.\n *\n * Features:\n * - Cloudinary client configuration (server-side only)\n * - Upload utilities with preset handling\n * - Image transformation helpers\n * - Media library integration\n *\n * Usage:\n * ```typescript\n * // Server-side only\n * import { cloudinary, uploadToCloudinary } from '@/lib/cloudinary'\n *\n * const result = await uploadToCloudinary(file, { folder: 'personnel' })\n * ```\n */ // Only import Cloudinary SDK on server-side\nlet cloudinary = null;\n// Initialize Cloudinary on server-side\nasync function initCloudinary() {\n    if ( true && !cloudinary) {\n        const cloudinaryModule = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/lodash\"), __webpack_require__.e(\"vendor-chunks/cloudinary\"), __webpack_require__.e(\"vendor-chunks/q\")]).then(__webpack_require__.t.bind(__webpack_require__, /*! cloudinary */ \"(rsc)/./node_modules/cloudinary/cloudinary.js\", 23));\n        cloudinary = cloudinaryModule.v2;\n        // Configure Cloudinary\n        if (cloudinary) {\n            cloudinary.config({\n                cloud_name: cloudName,\n                api_key: apiKey,\n                api_secret: apiSecret,\n                secure: true\n            });\n        }\n    }\n    return cloudinary;\n}\n// Validate required environment variables\nconst cloudName = \"dvwaviwn0\";\nconst apiKey = process.env.CLOUDINARY_API_KEY;\nconst apiSecret = process.env.CLOUDINARY_API_SECRET;\nif (!cloudName || !apiKey || !apiSecret) {\n    console.error('Missing Cloudinary environment variables:', {\n        cloudName: !!cloudName,\n        apiKey: !!apiKey,\n        apiSecret: !!apiSecret\n    });\n}\n// Cloudinary will be configured in initCloudinary function\n/**\n * Cloudinary upload presets configuration\n */ const UPLOAD_PRESETS = {\n    LGU_PROJECT: 'lgu_project',\n    PERSONNEL: 'lgu_project',\n    DOCUMENTS: 'lgu_project',\n    MEDIA: 'lgu_project' // General media uploads\n};\n/**\n * Cloudinary folders configuration\n */ const CLOUDINARY_FOLDERS = {\n    PERSONNEL: 'lgu-uploads/personnel',\n    DOCUMENTS: 'lgu-uploads/documents',\n    MEDIA: 'lgu-uploads/media',\n    TEMP: 'lgu-uploads/temp'\n};\n/**\n * Upload a file to Cloudinary (server-side only)\n * @param file - File to upload (can be File object, Buffer, or base64 string)\n * @param options - Upload options\n * @returns Promise<CloudinaryUploadResult>\n */ async function uploadToCloudinary(file, options = {}) {\n    const cloudinaryInstance = await initCloudinary();\n    if (!cloudinaryInstance) {\n        throw new Error('Cloudinary is not available. This function can only be used server-side.');\n    }\n    try {\n        console.log('[Cloudinary] Starting upload with options:', options);\n        // Convert File to base64 if needed\n        let uploadData;\n        if (file instanceof File) {\n            const buffer = await file.arrayBuffer();\n            const base64 = Buffer.from(buffer).toString('base64');\n            uploadData = `data:${file.type};base64,${base64}`;\n        } else if (Buffer.isBuffer(file)) {\n            uploadData = `data:application/octet-stream;base64,${file.toString('base64')}`;\n        } else {\n            uploadData = file;\n        }\n        // Default upload options\n        const uploadOptions = {\n            upload_preset: UPLOAD_PRESETS.LGU_PROJECT,\n            folder: options.folder || CLOUDINARY_FOLDERS.MEDIA,\n            resource_type: options.resource_type || 'auto',\n            tags: [\n                'lgu-project',\n                ...options.tags || []\n            ],\n            ...options\n        };\n        const result = await cloudinaryInstance.uploader.upload(uploadData, uploadOptions);\n        console.log('[Cloudinary] Upload successful:', result.public_id);\n        return result;\n    } catch (error) {\n        console.error('[Cloudinary] Upload failed:', error);\n        throw new Error(`Cloudinary upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n}\n/**\n * Delete a file from Cloudinary (server-side only)\n * @param publicId - Public ID of the file to delete\n * @param resourceType - Type of resource (image, video, raw)\n * @returns Promise<any>\n */ async function deleteFromCloudinary(publicId, resourceType = 'image') {\n    const cloudinaryInstance = await initCloudinary();\n    if (!cloudinaryInstance) {\n        throw new Error('Cloudinary is not available. This function can only be used server-side.');\n    }\n    try {\n        console.log('[Cloudinary] Deleting file:', publicId);\n        const result = await cloudinaryInstance.uploader.destroy(publicId, {\n            resource_type: resourceType\n        });\n        console.log('[Cloudinary] Delete result:', result);\n        return result;\n    } catch (error) {\n        console.error('[Cloudinary] Delete failed:', error);\n        throw new Error(`Cloudinary delete failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    }\n}\n/**\n * Generate a Cloudinary URL with transformations\n * @param publicId - Public ID of the image\n * @param transformations - Transformation options\n * @returns string - Cloudinary URL\n */ async function generateCloudinaryUrl(publicId, transformations = {}) {\n    try {\n        const cloudinaryInstance = await initCloudinary();\n        if (!cloudinaryInstance) {\n            console.error('[Cloudinary] Not available for URL generation');\n            return '';\n        }\n        return cloudinaryInstance.url(publicId, {\n            secure: true,\n            ...transformations\n        });\n    } catch (error) {\n        console.error('[Cloudinary] URL generation failed:', error);\n        return '';\n    }\n}\n/**\n * Get optimized image URL for different use cases\n */ const getOptimizedImageUrl = {\n    /**\n   * Profile photo optimization\n   */ profile: async (publicId, size = 200)=>await generateCloudinaryUrl(publicId, {\n            width: size,\n            height: size,\n            crop: 'fill',\n            gravity: 'face',\n            quality: 'auto',\n            format: 'auto'\n        }),\n    /**\n   * Thumbnail optimization\n   */ thumbnail: async (publicId, width = 150, height = 150)=>await generateCloudinaryUrl(publicId, {\n            width,\n            height,\n            crop: 'fill',\n            quality: 'auto',\n            format: 'auto'\n        }),\n    /**\n   * Full size image optimization\n   */ fullSize: async (publicId, maxWidth = 1200)=>await generateCloudinaryUrl(publicId, {\n            width: maxWidth,\n            crop: 'limit',\n            quality: 'auto',\n            format: 'auto'\n        }),\n    /**\n   * Document preview optimization\n   */ document: async (publicId, width = 800)=>await generateCloudinaryUrl(publicId, {\n            width,\n            crop: 'limit',\n            quality: 'auto',\n            format: 'auto'\n        })\n};\n// Export the configured Cloudinary instance\n\n// Export configuration constants\nconst CLOUDINARY_CONFIG = {\n    cloudName,\n    apiKey\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/cloudinary.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabaseMediaService.ts":
/*!*****************************************!*\
  !*** ./src/lib/supabaseMediaService.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseMediaService: () => (/* binding */ SupabaseMediaService)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/**\n * Supabase Media Service\n * \n * Enterprise-grade database service for media library with perfect bidirectional sync.\n * Handles all database operations for media assets with Supabase integration.\n * \n * Features:\n * - 🗄️ Complete CRUD operations for media assets\n * - 🔄 Sync status management\n * - 📊 Real-time statistics\n * - 🔍 Advanced search and filtering\n * - 📝 Audit trail logging\n * - 🔒 Enterprise-grade error handling\n * - ⚡ Optimized queries with indexes\n * \n * <AUTHOR> Project Team\n * @version 1.0.0\n */ \n/**\n * Supabase Media Service Class\n */ class SupabaseMediaService {\n    static{\n        this.supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://lkolpgpmdculqqfqyzaf.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY);\n    }\n    /**\n   * Create or update media asset in database\n   */ static async upsertMediaAsset(asset) {\n        try {\n            console.log('[SupabaseMediaService] Upserting media asset:', asset.cloudinary_public_id);\n            const { data, error } = await this.supabase.from('media_assets').upsert({\n                ...asset,\n                updated_at: new Date().toISOString(),\n                last_synced_at: new Date().toISOString()\n            }, {\n                onConflict: 'cloudinary_public_id'\n            }).select().single();\n            if (error) {\n                throw new Error(`Failed to upsert media asset: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error('[SupabaseMediaService] Upsert failed:', error);\n            throw error;\n        }\n    }\n    /**\n   * Get media asset by Cloudinary public ID\n   */ static async getMediaAssetByPublicId(publicId) {\n        try {\n            const { data, error } = await this.supabase.from('media_assets').select('*').eq('cloudinary_public_id', publicId).eq('deleted_at', null).single();\n            if (error && error.code !== 'PGRST116') {\n                throw new Error(`Failed to get media asset: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error('[SupabaseMediaService] Get asset failed:', error);\n            return null;\n        }\n    }\n    /**\n   * Search media assets with advanced filtering\n   */ static async searchMediaAssets(options = {}) {\n        try {\n            const { search, folder, resource_type, tags, sync_status, uploaded_by, date_from, date_to, min_size, max_size, page = 1, limit = 50, sort_by = 'created_at', sort_order = 'desc' } = options;\n            let query = this.supabase.from('media_assets').select('*', {\n                count: 'exact'\n            }).is('deleted_at', null);\n            // Apply filters\n            if (search) {\n                query = query.or(`original_filename.ilike.%${search}%,display_name.ilike.%${search}%,description.ilike.%${search}%`);\n            }\n            if (folder) {\n                query = query.eq('folder', folder);\n            }\n            if (resource_type) {\n                query = query.eq('resource_type', resource_type);\n            }\n            if (tags && tags.length > 0) {\n                query = query.overlaps('tags', tags);\n            }\n            if (sync_status) {\n                query = query.eq('sync_status', sync_status);\n            }\n            if (uploaded_by) {\n                query = query.eq('uploaded_by', uploaded_by);\n            }\n            if (date_from) {\n                query = query.gte('created_at', date_from);\n            }\n            if (date_to) {\n                query = query.lte('created_at', date_to);\n            }\n            if (min_size) {\n                query = query.gte('file_size', min_size);\n            }\n            if (max_size) {\n                query = query.lte('file_size', max_size);\n            }\n            // Apply sorting and pagination\n            const offset = (page - 1) * limit;\n            query = query.order(sort_by, {\n                ascending: sort_order === 'asc'\n            }).range(offset, offset + limit - 1);\n            const { data, error, count } = await query;\n            if (error) {\n                throw new Error(`Failed to search media assets: ${error.message}`);\n            }\n            const total = count || 0;\n            const totalPages = Math.ceil(total / limit);\n            return {\n                assets: data,\n                total,\n                page,\n                limit,\n                has_next: page < totalPages,\n                has_prev: page > 1\n            };\n        } catch (error) {\n            console.error('[SupabaseMediaService] Search failed:', error);\n            throw error;\n        }\n    }\n    /**\n   * Get media statistics\n   */ static async getMediaStats() {\n        try {\n            // Try the function first\n            const { data: functionData, error: functionError } = await this.supabase.rpc('get_media_statistics');\n            if (!functionError && functionData) {\n                return functionData[0];\n            }\n            // Fallback: Calculate stats manually\n            console.log('[SupabaseMediaService] Function not available, calculating stats manually...');\n            const { data: assets, error } = await this.supabase.from('media_assets').select('resource_type, file_size, sync_status').is('deleted_at', null);\n            if (error) {\n                console.error('[SupabaseMediaService] Failed to get media assets for stats:', error);\n                throw error;\n            }\n            const stats = {\n                total_assets: assets?.length || 0,\n                total_images: assets?.filter((a)=>a.resource_type === 'image').length || 0,\n                total_videos: assets?.filter((a)=>a.resource_type === 'video').length || 0,\n                total_raw: assets?.filter((a)=>a.resource_type === 'raw').length || 0,\n                total_size: assets?.reduce((sum, a)=>sum + (a.file_size || 0), 0) || 0,\n                synced_assets: assets?.filter((a)=>a.sync_status === 'synced').length || 0,\n                pending_assets: assets?.filter((a)=>a.sync_status === 'pending').length || 0,\n                error_assets: assets?.filter((a)=>a.sync_status === 'error').length || 0\n            };\n            return stats;\n        } catch (error) {\n            console.error('[SupabaseMediaService] Get stats failed:', error);\n            // Return default stats on error\n            return {\n                total_assets: 0,\n                total_images: 0,\n                total_videos: 0,\n                total_raw: 0,\n                total_size: 0,\n                synced_assets: 0,\n                pending_assets: 0,\n                error_assets: 0\n            };\n        }\n    }\n    /**\n   * Soft delete media asset\n   */ static async softDeleteMediaAsset(publicId, deletedBy) {\n        try {\n            console.log('[SupabaseMediaService] Soft deleting media asset:', publicId);\n            const { data, error } = await this.supabase.rpc('soft_delete_media_asset', {\n                asset_id: publicId,\n                deleted_by_user: deletedBy\n            });\n            if (error) {\n                throw new Error(`Failed to soft delete media asset: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error('[SupabaseMediaService] Soft delete failed:', error);\n            throw error;\n        }\n    }\n    /**\n   * Update sync status\n   */ static async updateSyncStatus(publicId, status, errorMessage) {\n        try {\n            const { data, error } = await this.supabase.rpc('update_media_sync_status', {\n                asset_id: publicId,\n                new_status: status,\n                error_msg: errorMessage\n            });\n            if (error) {\n                throw new Error(`Failed to update sync status: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error('[SupabaseMediaService] Update sync status failed:', error);\n            throw error;\n        }\n    }\n    /**\n   * Log sync operation\n   */ static async logSyncOperation(log) {\n        try {\n            const { data, error } = await this.supabase.from('media_sync_log').insert({\n                ...log,\n                created_at: new Date().toISOString()\n            }).select().single();\n            if (error) {\n                throw new Error(`Failed to log sync operation: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error('[SupabaseMediaService] Log sync operation failed:', error);\n            throw error;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabaseMediaService.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcloudinary%2Fupload%2Froute&page=%2Fapi%2Fcloudinary%2Fupload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcloudinary%2Fupload%2Froute.ts&appDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDABBIE%5COneDrive%5CDesktop%5Clgu-project-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();